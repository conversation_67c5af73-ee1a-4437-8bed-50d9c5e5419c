// pages/address/address.js
Page({
  data: {
    addresses: [],
    selectMode: false, // 是否为选择地址模式
    selectedAddressId: null
  },

  onLoad(options) {
    // 检查是否为选择地址模式
    this.setData({
      selectMode: options.select === 'true',
      selectedAddressId: options.selectedId || null
    })
    
    this.loadAddresses()
  },

  onShow() {
    this.loadAddresses()
  },

  // 加载地址列表
  loadAddresses() {
    const app = getApp()
    const addresses = app.getAddresses()
    this.setData({
      addresses: addresses
    })
  },

  // 选择地址（选择模式下）
  onSelectAddress(e) {
    if (!this.data.selectMode) return
    
    const addressId = e.currentTarget.dataset.id
    const pages = getCurrentPages()
    const prevPage = pages[pages.length - 2]
    
    if (prevPage) {
      prevPage.setData({
        selectedAddress: this.data.addresses.find(addr => addr.id === addressId)
      })
    }
    
    wx.navigateBack()
  },

  // 添加地址
  onAddAddress() {
    wx.navigateTo({
      url: '/pages/address-edit/address-edit'
    })
  },

  // 编辑地址
  onEditAddress(e) {
    const addressId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/address-edit/address-edit?id=${addressId}`
    })
  },

  // 设置默认地址
  onSetDefault(e) {
    const addressId = e.currentTarget.dataset.id
    const app = getApp()
    
    wx.showLoading({
      title: '设置中...'
    })
    
    setTimeout(() => {
      wx.hideLoading()
      
      if (app.setDefaultAddress(addressId)) {
        wx.showToast({
          title: '设置成功',
          icon: 'success'
        })
        this.loadAddresses()
      } else {
        wx.showToast({
          title: '设置失败',
          icon: 'error'
        })
      }
    }, 500)
  },

  // 删除地址
  onDeleteAddress(e) {
    const addressId = e.currentTarget.dataset.id
    const index = e.currentTarget.dataset.index
    const address = this.data.addresses[index]
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除地址"${address.name} ${address.phone}"吗？`,
      confirmText: '删除',
      confirmColor: '#ff6b35',
      success: (res) => {
        if (res.confirm) {
          this.deleteAddress(addressId)
        }
      }
    })
  },

  // 执行删除地址
  deleteAddress(addressId) {
    wx.showLoading({
      title: '删除中...'
    })
    
    setTimeout(() => {
      wx.hideLoading()
      
      const app = getApp()
      if (app.deleteAddress(addressId)) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
        this.loadAddresses()
      } else {
        wx.showToast({
          title: '删除失败',
          icon: 'error'
        })
      }
    }, 500)
  }
})
