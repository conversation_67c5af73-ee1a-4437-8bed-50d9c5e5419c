<!--detail.wxml-->
<scroll-view class="container" scroll-y="true">
  <!-- 商品图片 -->
  <view class="image-container">
    <image src="{{product.image}}" class="product-image" mode="aspectFill"></image>
    <view class="back-btn" bindtap="onBack">
      <icon type="cancel" size="20" color="#fff"></icon>
    </view>
  </view>

  <!-- 商品信息 -->
  <view class="product-info">
    <view class="product-header">
      <text class="product-name">{{product.name}}</text>
      <text class="product-price">¥{{product.price}}</text>
    </view>
    <text class="product-desc">{{product.description}}</text>

    <!-- 营养信息 -->
    <view class="nutrition-info" wx:if="{{product.nutrition}}">
      <text class="section-title">营养信息</text>
      <view class="nutrition-grid">
        <view class="nutrition-item" wx:for="{{product.nutrition}}" wx:key="name">
          <text class="nutrition-name">{{item.name}}</text>
          <text class="nutrition-value">{{item.value}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 规格选择 -->
  <view class="spec-section" wx:if="{{product.specs && product.specs.length > 0}}">
    <text class="section-title">选择规格</text>
    <view class="spec-list">
      <view class="spec-item {{selectedSpec === item.id ? 'active' : ''}}"
            wx:for="{{product.specs}}" wx:key="id"
            bindtap="onSpecTap" data-id="{{item.id}}">
        <text class="spec-name">{{item.name}}</text>
        <text class="spec-price">+¥{{item.extraPrice}}</text>
      </view>
    </view>
  </view>

  <!-- 数量选择 -->
  <view class="quantity-section">
    <text class="section-title">数量</text>
    <view class="quantity-control">
      <view class="quantity-btn {{quantity <= 1 ? 'disabled' : ''}}" bindtap="onQuantityChange" data-type="minus">
        <text class="quantity-text">-</text>
      </view>
      <text class="quantity-value">{{quantity}}</text>
      <view class="quantity-btn" bindtap="onQuantityChange" data-type="plus">
        <text class="quantity-text">+</text>
      </view>
    </view>
  </view>

  <!-- 商品详情 -->
  <view class="detail-section">
    <text class="section-title">商品详情</text>
    <text class="detail-content">{{product.detail || '这是一款精心制作的轻食产品，选用优质食材，营养均衡，口感丰富。适合注重健康饮食的您。'}}</text>
  </view>
</scroll-view>

<!-- 底部操作栏 -->
<view class="bottom-bar">
  <view class="total-price">
    <text class="price-label">总价：</text>
    <text class="price-value">¥{{totalPrice}}</text>
  </view>
  <view class="action-buttons">
    <button class="cart-btn" bindtap="onAddToCart">加入购物车</button>
    <button class="buy-btn" bindtap="onBuyNow">立即购买</button>
  </view>
</view>