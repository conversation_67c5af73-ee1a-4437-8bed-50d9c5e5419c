// pages/cart/cart.js
Page({
  data: {
    cartItems: [],
    allSelected: false,
    selectedCount: 0,
    totalPrice: '0.00'
  },

  onLoad() {
    this.loadCartData()
  },

  onShow() {
    this.loadCartData()
  },

  // 加载购物车数据
  loadCartData() {
    const app = getApp()
    const cartItems = app.getCartItems()

    this.setData({
      cartItems: cartItems
    })
    this.calculateTotal()
  },

  // 选择单个商品
  onSelectItem(e) {
    const cartId = e.currentTarget.dataset.id
    const cartItems = this.data.cartItems.map(item => {
      if (item.cartId === cartId) {
        item.selected = !item.selected
      }
      return item
    })

    this.setData({
      cartItems: cartItems
    })
    this.calculateTotal()
    this.updateAppCart()
  },

  // 全选/取消全选
  onSelectAll() {
    const allSelected = !this.data.allSelected
    const cartItems = this.data.cartItems.map(item => {
      item.selected = allSelected
      return item
    })

    this.setData({
      cartItems: cartItems,
      allSelected: allSelected
    })
    this.calculateTotal()
    this.updateAppCart()
  },

  // 数量调整
  onQuantityChange(e) {
    const cartId = e.currentTarget.dataset.id
    const type = e.currentTarget.dataset.type

    const cartItems = this.data.cartItems.map(item => {
      if (item.cartId === cartId) {
        if (type === 'plus') {
          item.quantity++
        } else if (type === 'minus' && item.quantity > 1) {
          item.quantity--
        }
        // 重新计算单项总价
        let price = item.price
        if (item.spec && item.spec.extraPrice) {
          price += item.spec.extraPrice
        }
        item.totalPrice = (price * item.quantity).toFixed(2)
      }
      return item
    })

    this.setData({
      cartItems: cartItems
    })
    this.calculateTotal()
    this.updateAppCart()
  },

  // 删除商品
  onDeleteItem(e) {
    const cartId = e.currentTarget.dataset.id

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个商品吗？',
      success: (res) => {
        if (res.confirm) {
          const cartItems = this.data.cartItems.filter(item => item.cartId !== cartId)
          this.setData({
            cartItems: cartItems
          })
          this.calculateTotal()
          this.updateAppCart()

          wx.showToast({
            title: '删除成功',
            icon: 'success'
          })
        }
      }
    })
  },

  // 计算总价和选中数量
  calculateTotal() {
    const selectedItems = this.data.cartItems.filter(item => item.selected)
    const selectedCount = selectedItems.length
    const totalPrice = selectedItems.reduce((sum, item) => {
      return sum + parseFloat(item.totalPrice)
    }, 0).toFixed(2)

    const allSelected = this.data.cartItems.length > 0 && selectedCount === this.data.cartItems.length

    this.setData({
      selectedCount: selectedCount,
      totalPrice: totalPrice,
      allSelected: allSelected
    })
  },

  // 更新全局购物车数据
  updateAppCart() {
    const app = getApp()
    app.updateCart(this.data.cartItems)
  },

  // 去购物
  onGoShopping() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  // 结算
  onCheckout() {
    if (this.data.selectedCount === 0) {
      wx.showToast({
        title: '请选择要结算的商品',
        icon: 'none'
      })
      return
    }

    // 检查登录状态
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再进行结算',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login?from=cart'
            })
          }
        }
      })
      return
    }

    const selectedItems = this.data.cartItems.filter(item => item.selected)

    // 显示结算详情
    const itemList = selectedItems.map(item =>
      `${item.name} x${item.quantity} = ¥${item.totalPrice}`
    ).join('\n')

    wx.showModal({
      title: '确认结算',
      content: `${itemList}\n\n总计：¥${this.data.totalPrice}`,
      confirmText: '确认支付',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.processPayment(selectedItems)
        }
      }
    })
  },

  // 处理支付
  processPayment(selectedItems) {
    wx.showLoading({
      title: '支付中...'
    })

    // 模拟支付延迟
    setTimeout(() => {
      wx.hideLoading()

      // 创建订单
      const app = getApp()
      const orderData = {
        items: selectedItems,
        totalPrice: this.data.totalPrice,
        totalQuantity: this.data.selectedCount,
        address: '默认收货地址', // 实际应用中从地址管理获取
        remark: ''
      }

      const order = app.createOrder(orderData)

      // 模拟订单状态变化
      setTimeout(() => {
        app.updateOrderStatus(order.id, 'processing')
      }, 3000)

      setTimeout(() => {
        app.updateOrderStatus(order.id, 'completed')
      }, 10000)

      wx.showToast({
        title: '支付成功！',
        icon: 'success',
        duration: 2000
      })

      // 移除已结算的商品
      const remainingItems = this.data.cartItems.filter(item => !item.selected)
      this.setData({
        cartItems: remainingItems
      })
      this.calculateTotal()
      this.updateAppCart()

      // 显示订单信息
      setTimeout(() => {
        wx.showModal({
          title: '订单创建成功',
          content: `订单号：${order.id}\n您可以在个人中心查看订单详情`,
          confirmText: '查看订单',
          cancelText: '继续购物',
          success: (modalRes) => {
            if (modalRes.confirm) {
              wx.switchTab({
                url: '/pages/profile/profile'
              })
            }
          }
        })
      }, 2000)
    }, 1500)
  }
})