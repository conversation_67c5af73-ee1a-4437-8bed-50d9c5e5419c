<!--address-edit.wxml-->
<scroll-view class="container" scroll-y="true">
  <form bindsubmit="onSubmit">
    <!-- 联系人信息 -->
    <view class="form-section">
      <view class="section-title">联系人信息</view>
      
      <view class="form-item">
        <text class="form-label">收货人</text>
        <input class="form-input" name="name" placeholder="请输入收货人姓名" 
               value="{{formData.name}}" bindinput="onInputChange" data-field="name" maxlength="20"></input>
      </view>
      
      <view class="form-item">
        <text class="form-label">手机号</text>
        <input class="form-input" name="phone" type="number" placeholder="请输入手机号" 
               value="{{formData.phone}}" bindinput="onInputChange" data-field="phone" maxlength="11"></input>
      </view>
    </view>

    <!-- 收货地址 -->
    <view class="form-section">
      <view class="section-title">收货地址</view>
      
      <!-- 地区选择 -->
      <view class="form-item" bindtap="onSelectRegion">
        <text class="form-label">所在地区</text>
        <view class="region-selector">
          <text class="region-text {{!formData.province ? 'placeholder' : ''}}">
            {{formData.province && formData.city && formData.district ? 
              formData.province + ' ' + formData.city + ' ' + formData.district : 
              '请选择省市区'}}
          </text>
          <icon type="next" size="16" color="#ccc"></icon>
        </view>
      </view>
      
      <!-- 详细地址 -->
      <view class="form-item">
        <text class="form-label">详细地址</text>
        <textarea class="form-textarea" name="detail" placeholder="请输入详细地址（街道、门牌号等）" 
                  value="{{formData.detail}}" bindinput="onInputChange" data-field="detail" 
                  maxlength="100" auto-height></textarea>
      </view>
      
      <!-- 地图选点 -->
      <view class="map-section">
        <view class="map-header">
          <text class="map-title">地图选点</text>
          <text class="map-tip">拖动地图中心点选择精确位置</text>
        </view>
        <view class="map-container">
          <map class="map" 
               latitude="{{mapData.latitude}}" 
               longitude="{{mapData.longitude}}"
               markers="{{mapData.markers}}"
               bindregionchange="onMapRegionChange"
               show-location="true">
          </map>
          <view class="map-center-point"></view>
        </view>
        <button class="location-btn" bindtap="onGetLocation">
          <icon type="success_no_circle" size="16" color="#ff6b35"></icon>
          <text>获取当前位置</text>
        </button>
      </view>
    </view>

    <!-- 设置选项 -->
    <view class="form-section">
      <view class="form-item">
        <text class="form-label">设为默认地址</text>
        <switch name="isDefault" checked="{{formData.isDefault}}" 
                bindchange="onSwitchChange" data-field="isDefault" color="#ff6b35"></switch>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="submit-btn" form-type="submit" disabled="{{!canSubmit}}">
        {{isEdit ? '保存地址' : '添加地址'}}
      </button>
    </view>
  </form>
</scroll-view>
