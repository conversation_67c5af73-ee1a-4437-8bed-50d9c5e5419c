// pages/profile/profile.js
Page({
  data: {
    isLoggedIn: false,
    userInfo: {
      avatarUrl: '',
      nickName: '',
      phone: ''
    },
    orderStats: {
      total: 12,
      pending: 2,
      processing: 1,
      completed: 9
    },
    couponCount: 3
  },

  onLoad() {
    this.checkLoginStatus()
  },

  onShow() {
    this.checkLoginStatus()
    this.loadOrderStats()
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({
        isLoggedIn: true,
        userInfo: userInfo
      })
    }
  },

  // 加载订单统计
  loadOrderStats() {
    if (this.data.isLoggedIn) {
      const app = getApp()
      const orderStats = app.getOrderStats()
      this.setData({
        orderStats: orderStats
      })
    } else {
      // 未登录时显示默认数据
      this.setData({
        orderStats: {
          total: 0,
          pending: 0,
          processing: 0,
          completed: 0
        }
      })
    }
  },

  // 登录
  onLogin() {
    wx.navigateTo({
      url: '/pages/login/login?from=profile'
    })
  },

  // 退出登录
  onLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('userInfo')
          this.setData({
            isLoggedIn: false,
            userInfo: {
              avatarUrl: '',
              nickName: '',
              phone: ''
            }
          })

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  },

  // 订单点击
  onOrderTap(e) {
    if (!this.data.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录后查看订单',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.onLogin()
          }
        }
      })
      return
    }

    const status = e.currentTarget.dataset.status
    this.showOrderList(status)
  },

  // 显示订单列表
  showOrderList(status) {
    const app = getApp()
    const orders = status === 'all' ? app.getOrders() : app.getOrders(status)

    const statusText = {
      'all': '全部',
      'pending': '待付款',
      'processing': '制作中',
      'completed': '已完成'
    }

    if (orders.length === 0) {
      wx.showModal({
        title: `${statusText[status]}订单`,
        content: '暂无相关订单',
        showCancel: false
      })
      return
    }

    // 构建订单列表显示内容
    const orderList = orders.slice(0, 5).map(order => {
      const statusMap = {
        'pending': '待付款',
        'processing': '制作中',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      const itemNames = order.items.map(item => item.name).join('、')
      return `${order.id}\n${itemNames}\n¥${order.totalPrice} - ${statusMap[order.status]}`
    }).join('\n\n')

    const content = orders.length > 5
      ? `${orderList}\n\n... 还有${orders.length - 5}个订单`
      : orderList

    wx.showModal({
      title: `${statusText[status]}订单`,
      content: content,
      confirmText: '查看详情',
      cancelText: '关闭',
      success: (res) => {
        if (res.confirm) {
          this.showOrderDetail(orders[0])
        }
      }
    })
  },

  // 显示订单详情
  showOrderDetail(order) {
    const statusMap = {
      'pending': '待付款',
      'processing': '制作中',
      'completed': '已完成',
      'cancelled': '已取消'
    }

    const itemList = order.items.map(item =>
      `${item.name} x${item.quantity} = ¥${item.totalPrice}`
    ).join('\n')

    const content = `订单号：${order.id}
状态：${statusMap[order.status]}
创建时间：${new Date(order.createTime).toLocaleString()}

商品清单：
${itemList}

总计：¥${order.totalPrice}
收货地址：${order.address}`

    wx.showModal({
      title: '订单详情',
      content: content,
      showCancel: false
    })
  },

  // 菜单点击
  onMenuTap(e) {
    const type = e.currentTarget.dataset.type

    // 检查是否需要登录
    if (!this.data.isLoggedIn && ['address', 'coupon', 'favorite', 'history'].includes(type)) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再使用此功能',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.onLogin()
          }
        }
      })
      return
    }

    switch (type) {
      case 'address':
        this.showAddressManagement()
        break
      case 'coupon':
        this.showCouponCenter()
        break
      case 'favorite':
        this.showFavoriteList()
        break
      case 'history':
        this.showBrowseHistory()
        break
      case 'feedback':
        this.showFeedback()
        break
      case 'about':
        wx.showModal({
          title: '关于我们',
          content: '轻食点餐小程序 v1.0\n\n专注健康轻食，为您提供营养美味的用餐体验。\n\n联系我们：************\n邮箱：<EMAIL>',
          showCancel: false
        })
        break
      case 'settings':
        this.showSettings()
        break
      default:
        break
    }
  },

  // 地址管理
  showAddressManagement() {
    wx.showActionSheet({
      itemList: ['添加新地址', '管理现有地址'],
      success: (res) => {
        if (res.tapIndex === 0) {
          wx.showToast({
            title: '添加地址功能开发中',
            icon: 'none'
          })
        } else {
          wx.showToast({
            title: '地址管理功能开发中',
            icon: 'none'
          })
        }
      }
    })
  },

  // 优惠券中心
  showCouponCenter() {
    wx.showModal({
      title: '我的优惠券',
      content: `您有 ${this.data.couponCount} 张可用优惠券\n\n• 新用户专享券 ¥10\n• 满50减8券 ¥8\n• 免配送费券 ¥5`,
      confirmText: '查看详情',
      cancelText: '关闭',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '优惠券详情页开发中',
            icon: 'none'
          })
        }
      }
    })
  },

  // 收藏列表
  showFavoriteList() {
    wx.showModal({
      title: '我的收藏',
      content: '您还没有收藏任何商品\n快去首页看看有什么喜欢的吧！',
      confirmText: '去逛逛',
      cancelText: '关闭',
      success: (res) => {
        if (res.confirm) {
          wx.switchTab({
            url: '/pages/index/index'
          })
        }
      }
    })
  },

  // 浏览历史
  showBrowseHistory() {
    wx.showModal({
      title: '浏览历史',
      content: '最近浏览：\n• 凯撒沙拉\n• 牛油果吐司\n• 鸡胸肉沙拉',
      confirmText: '清空历史',
      cancelText: '关闭',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '历史记录已清空',
            icon: 'success'
          })
        }
      }
    })
  },

  // 意见反馈
  showFeedback() {
    wx.showActionSheet({
      itemList: ['产品建议', '服务投诉', '其他问题'],
      success: (res) => {
        const feedbackTypes = ['产品建议', '服务投诉', '其他问题']
        wx.showToast({
          title: `${feedbackTypes[res.tapIndex]}功能开发中`,
          icon: 'none'
        })
      }
    })
  },

  // 设置
  showSettings() {
    wx.showActionSheet({
      itemList: ['消息通知设置', '隐私设置', '清除缓存', '检查更新'],
      success: (res) => {
        const settingTypes = ['消息通知设置', '隐私设置', '清除缓存', '检查更新']
        if (res.tapIndex === 2) {
          // 清除缓存
          wx.showModal({
            title: '清除缓存',
            content: '确定要清除所有缓存数据吗？',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.clearStorageSync()
                wx.showToast({
                  title: '缓存已清除',
                  icon: 'success'
                })
              }
            }
          })
        } else {
          wx.showToast({
            title: `${settingTypes[res.tapIndex]}功能开发中`,
            icon: 'none'
          })
        }
      }
    })
  }
})