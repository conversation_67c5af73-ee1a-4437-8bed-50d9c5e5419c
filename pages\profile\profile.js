// pages/profile/profile.js
Page({
  data: {
    isLoggedIn: false,
    userInfo: {
      avatarUrl: '',
      nickName: '',
      phone: ''
    },
    orderStats: {
      total: 12,
      pending: 2,
      processing: 1,
      completed: 9
    },
    couponCount: 3
  },

  onLoad() {
    this.checkLoginStatus()
  },

  onShow() {
    this.checkLoginStatus()
    this.loadOrderStats()
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({
        isLoggedIn: true,
        userInfo: userInfo
      })
    }
  },

  // 加载订单统计
  loadOrderStats() {
    // 这里可以调用API获取真实的订单统计数据
    // 目前使用模拟数据
    this.setData({
      orderStats: {
        total: Math.floor(Math.random() * 20) + 5,
        pending: Math.floor(Math.random() * 5),
        processing: Math.floor(Math.random() * 3),
        completed: Math.floor(Math.random() * 15) + 5
      }
    })
  },

  // 登录
  onLogin() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        const userInfo = {
          avatarUrl: res.userInfo.avatarUrl,
          nickName: res.userInfo.nickName,
          phone: '138****8888' // 模拟手机号
        }

        // 保存用户信息到本地存储
        wx.setStorageSync('userInfo', userInfo)

        this.setData({
          isLoggedIn: true,
          userInfo: userInfo
        })

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })
      },
      fail: () => {
        wx.showToast({
          title: '登录取消',
          icon: 'none'
        })
      }
    })
  },

  // 退出登录
  onLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('userInfo')
          this.setData({
            isLoggedIn: false,
            userInfo: {
              avatarUrl: '',
              nickName: '',
              phone: ''
            }
          })

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  },

  // 订单点击
  onOrderTap(e) {
    const status = e.currentTarget.dataset.status
    wx.showToast({
      title: `查看${status === 'all' ? '全部' : status === 'pending' ? '待付款' : status === 'processing' ? '制作中' : '已完成'}订单`,
      icon: 'none'
    })
  },

  // 菜单点击
  onMenuTap(e) {
    const type = e.currentTarget.dataset.type

    switch (type) {
      case 'address':
        wx.showToast({
          title: '收货地址管理',
          icon: 'none'
        })
        break
      case 'coupon':
        wx.showToast({
          title: '优惠券中心',
          icon: 'none'
        })
        break
      case 'favorite':
        wx.showToast({
          title: '我的收藏',
          icon: 'none'
        })
        break
      case 'history':
        wx.showToast({
          title: '浏览历史',
          icon: 'none'
        })
        break
      case 'feedback':
        wx.showToast({
          title: '意见反馈',
          icon: 'none'
        })
        break
      case 'about':
        wx.showModal({
          title: '关于我们',
          content: '轻食点餐小程序 v1.0\n专注健康轻食，为您提供营养美味的用餐体验。',
          showCancel: false
        })
        break
      case 'settings':
        wx.showToast({
          title: '设置',
          icon: 'none'
        })
        break
      default:
        break
    }
  }
})