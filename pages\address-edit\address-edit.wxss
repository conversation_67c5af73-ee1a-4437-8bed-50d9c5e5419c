/* address-edit.wxss */
page {
  background-color: #f8f8f8;
}

.container {
  min-height: 100vh;
  padding-bottom: 150rpx;
}

/* 表单区域样式 */
.form-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 0 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  padding: 30rpx 0 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  width: 160rpx;
  flex-shrink: 0;
}

.form-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  padding: 0 20rpx;
}

.form-textarea {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  min-height: 120rpx;
}

/* 地区选择样式 */
.region-selector {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
}

.region-text {
  font-size: 28rpx;
  color: #333;
}

.region-text.placeholder {
  color: #999;
}

.region-selector:active {
  background-color: #f5f5f5;
}

/* 地图样式 */
.map-section {
  padding: 30rpx 0;
}

.map-header {
  margin-bottom: 20rpx;
}

.map-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.map-tip {
  font-size: 24rpx;
  color: #999;
}

.map-container {
  position: relative;
  height: 400rpx;
  border-radius: 15rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.map {
  width: 100%;
  height: 100%;
}

.map-center-point {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20rpx;
  height: 20rpx;
  background-color: #ff6b35;
  border: 4rpx solid #fff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.3);
}

.location-btn {
  background-color: #f8f8f8;
  color: #ff6b35;
  border: 2rpx solid #ff6b35;
  border-radius: 50rpx;
  padding: 15rpx 30rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 200rpx;
  margin: 0 auto;
}

.location-btn icon {
  margin-right: 10rpx;
}

/* 提交按钮样式 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  padding: 30rpx;
  z-index: 100;
}

.submit-btn {
  width: 100%;
  background-color: #ff6b35;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 25rpx 0;
  font-size: 32rpx;
  font-weight: bold;
}

.submit-btn[disabled] {
  background-color: #ccc;
}

/* 开关样式 */
switch {
  transform: scale(0.8);
}
