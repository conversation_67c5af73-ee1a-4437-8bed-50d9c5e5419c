<!--address.wxml-->
<view class="container">
  <!-- 地址列表 -->
  <scroll-view class="address-list" scroll-y="true" wx:if="{{addresses.length > 0}}">
    <view class="address-item" wx:for="{{addresses}}" wx:key="id">
      <!-- 地址信息 -->
      <view class="address-content" bindtap="onSelectAddress" data-id="{{item.id}}">
        <view class="address-header">
          <view class="contact-info">
            <text class="contact-name">{{item.name}}</text>
            <text class="contact-phone">{{item.phone}}</text>
          </view>
          <view class="default-tag" wx:if="{{item.isDefault}}">默认</view>
        </view>
        <text class="address-detail">{{item.fullAddress}}</text>
      </view>
      
      <!-- 操作按钮 -->
      <view class="address-actions">
        <button class="action-btn edit-btn" bindtap="onEditAddress" data-id="{{item.id}}" data-index="{{index}}">
          <icon type="success_no_circle" size="16" color="#ff6b35"></icon>
          <text>编辑</text>
        </button>
        <button class="action-btn default-btn {{item.isDefault ? 'active' : ''}}" 
                bindtap="onSetDefault" data-id="{{item.id}}" disabled="{{item.isDefault}}">
          <icon type="{{item.isDefault ? 'success' : 'circle'}}" size="16" color="{{item.isDefault ? '#ff6b35' : '#999'}}"></icon>
          <text>{{item.isDefault ? '默认地址' : '设为默认'}}</text>
        </button>
        <button class="action-btn delete-btn" bindtap="onDeleteAddress" data-id="{{item.id}}" data-index="{{index}}">
          <icon type="cancel" size="16" color="#999"></icon>
          <text>删除</text>
        </button>
      </view>
    </view>
  </scroll-view>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <image src="/images/empty-address.png" class="empty-image"></image>
    <text class="empty-text">还没有收货地址</text>
    <text class="empty-tip">添加收货地址，享受便捷配送服务</text>
  </view>
</view>

<!-- 底部添加按钮 -->
<view class="bottom-bar">
  <button class="add-address-btn" bindtap="onAddAddress">
    <icon type="success_no_circle" size="20" color="#fff"></icon>
    <text>添加新地址</text>
  </button>
</view>
