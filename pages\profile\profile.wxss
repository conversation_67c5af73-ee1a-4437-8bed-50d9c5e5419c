/* profile.wxss */
page {
  background-color: #f8f8f8;
}

.container {
  padding-bottom: 40rpx;
}

/* 用户信息头部样式 */
.user-header {
  background: linear-gradient(135deg, #ff6b35, #ff8c42);
  padding: 60rpx 30rpx 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 10rpx;
}

.user-phone {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.login-btn {
  background-color: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  border-radius: 50rpx;
  padding: 15rpx 30rpx;
}

.login-text {
  color: #fff;
  font-size: 26rpx;
}

/* 订单统计样式 */
.order-stats {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: block;
}

.stats-list {
  display: flex;
  justify-content: space-around;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stats-number {
  font-size: 40rpx;
  font-weight: bold;
  color: #ff6b35;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 菜单样式 */
.menu-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-left {
  display: flex;
  align-items: center;
}

.menu-text {
  font-size: 28rpx;
  color: #333;
  margin-left: 20rpx;
}

.menu-right {
  display: flex;
  align-items: center;
}

.menu-badge {
  background-color: #ff6b35;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 10rpx;
  min-width: 30rpx;
  text-align: center;
}

/* 退出登录样式 */
.logout-section {
  padding: 40rpx 30rpx;
}

.logout-btn {
  background-color: #fff;
  color: #ff6b35;
  border: 2rpx solid #ff6b35;
  border-radius: 50rpx;
  padding: 25rpx 0;
  font-size: 28rpx;
  width: 100%;
}