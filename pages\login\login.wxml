<!--login.wxml-->
<view class="container">
  <!-- 顶部装饰 -->
  <view class="header">
    <image src="/images/logo.png" class="logo" mode="aspectFit"></image>
    <text class="app-name">轻食点餐</text>
    <text class="app-slogan">健康生活，从轻食开始</text>
  </view>

  <!-- 登录表单 -->
  <view class="login-form">
    <!-- 手机号登录 -->
    <view class="login-section" wx:if="{{loginType === 'phone'}}">
      <view class="input-group">
        <view class="input-item">
          <icon type="success_no_circle" size="20" color="#ff6b35"></icon>
          <input type="number" placeholder="请输入手机号" maxlength="11" bindinput="onPhoneInput" value="{{phone}}" class="input-field"></input>
        </view>
        <view class="input-item">
          <icon type="info" size="20" color="#ff6b35"></icon>
          <input type="number" placeholder="请输入验证码" maxlength="6" bindinput="onCodeInput" value="{{code}}" class="input-field"></input>
          <button class="code-btn {{canSendCode ? '' : 'disabled'}}" bindtap="onSendCode" disabled="{{!canSendCode}}">
            {{codeText}}
          </button>
        </view>
      </view>
      <button class="login-btn" bindtap="onPhoneLogin" disabled="{{!canLogin}}">登录</button>
    </view>

    <!-- 微信授权登录 -->
    <view class="login-section" wx:if="{{loginType === 'wechat'}}">
      <view class="wechat-login">
        <image src="/images/wechat-avatar.png" class="wechat-avatar"></image>
        <text class="wechat-tip">使用微信账号快速登录</text>
        <button class="wechat-btn" open-type="getUserProfile" bindgetuserprofile="onWechatLogin">
          <icon type="success" size="20" color="#fff"></icon>
          <text>微信授权登录</text>
        </button>
      </view>
    </view>

    <!-- 登录方式切换 -->
    <view class="login-switch">
      <text class="switch-text">其他登录方式：</text>
      <text class="switch-btn {{loginType === 'phone' ? '' : 'active'}}" bindtap="onSwitchLogin" data-type="wechat">微信登录</text>
      <text class="switch-divider">|</text>
      <text class="switch-btn {{loginType === 'phone' ? 'active' : ''}}" bindtap="onSwitchLogin" data-type="phone">手机登录</text>
    </view>

    <!-- 协议条款 -->
    <view class="agreement">
      <checkbox-group bindchange="onAgreementChange">
        <checkbox value="agree" checked="{{agreed}}" color="#ff6b35"></checkbox>
      </checkbox-group>
      <text class="agreement-text">我已阅读并同意</text>
      <text class="agreement-link" bindtap="onShowAgreement" data-type="user">《用户协议》</text>
      <text class="agreement-text">和</text>
      <text class="agreement-link" bindtap="onShowAgreement" data-type="privacy">《隐私政策》</text>
    </view>
  </view>

  <!-- 快速体验 -->
  <view class="quick-experience">
    <text class="quick-text">暂不登录，</text>
    <text class="quick-btn" bindtap="onQuickExperience">先去逛逛</text>
  </view>
</view>
