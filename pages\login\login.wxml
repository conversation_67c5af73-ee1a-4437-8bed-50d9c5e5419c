<!--login.wxml-->
<view class="container">
  <!-- 顶部装饰 -->
  <view class="header">
    <image src="/images/logo.png" class="logo" mode="aspectFit"></image>
    <text class="app-name">轻食点餐</text>
    <text class="app-slogan">健康生活，从轻食开始</text>
  </view>

  <!-- 登录表单 -->
  <view class="login-form">
    <!-- 账号密码登录 -->
    <view class="login-section">
      <view class="input-group">
        <view class="input-item">
          <icon type="success_no_circle" size="20" color="#ff6b35"></icon>
          <input type="text" placeholder="请输入账号/手机号" bindinput="onUsernameInput" value="{{username}}" class="input-field"></input>
        </view>
        <view class="input-item">
          <icon type="info" size="20" color="#ff6b35"></icon>
          <input type="password" placeholder="请输入密码" bindinput="onPasswordInput" value="{{password}}" class="input-field"></input>
        </view>
      </view>

      <!-- 记住密码和忘记密码 -->
      <view class="login-options">
        <view class="remember-password">
          <checkbox-group bindchange="onRememberChange">
            <checkbox value="remember" checked="{{rememberPassword}}" color="#ff6b35"></checkbox>
          </checkbox-group>
          <text class="remember-text">记住密码</text>
        </view>
        <text class="forgot-password" bindtap="onForgotPassword">忘记密码？</text>
      </view>

      <button class="login-btn" bindtap="onLogin" disabled="{{!canLogin}}">登录</button>

      <!-- 注册链接 -->
      <view class="register-link">
        <text class="register-text">还没有账号？</text>
        <text class="register-btn" bindtap="onRegister">立即注册</text>
      </view>
    </view>

    <!-- 协议条款 -->
    <view class="agreement">
      <checkbox-group bindchange="onAgreementChange">
        <checkbox value="agree" checked="{{agreed}}" color="#ff6b35"></checkbox>
      </checkbox-group>
      <text class="agreement-text">我已阅读并同意</text>
      <text class="agreement-link" bindtap="onShowAgreement" data-type="user">《用户协议》</text>
      <text class="agreement-text">和</text>
      <text class="agreement-link" bindtap="onShowAgreement" data-type="privacy">《隐私政策》</text>
    </view>
  </view>

  <!-- 快速体验 -->
  <view class="quick-experience">
    <text class="quick-text">暂不登录，</text>
    <text class="quick-btn" bindtap="onQuickExperience">先去逛逛</text>
  </view>
</view>
