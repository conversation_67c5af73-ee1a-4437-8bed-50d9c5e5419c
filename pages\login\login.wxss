/* login.wxss */
page {
  background: linear-gradient(135deg, #ff6b35, #ff8c42);
  height: 100vh;
}

.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 0 60rpx;
}

/* 顶部装饰 */
.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 0 80rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.app-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 15rpx;
}

.app-slogan {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 登录表单 */
.login-form {
  flex: 1;
  background-color: #fff;
  border-radius: 30rpx 30rpx 0 0;
  padding: 60rpx 40rpx;
  margin-top: 40rpx;
}

.login-section {
  margin-bottom: 40rpx;
}

/* 输入组 */
.input-group {
  margin-bottom: 40rpx;
}

.input-item {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 50rpx;
  padding: 25rpx 30rpx;
  margin-bottom: 30rpx;
}

.input-item icon {
  margin-right: 20rpx;
}

.input-field {
  flex: 1;
  font-size: 28rpx;
  border: none;
  background: transparent;
}

/* 登录选项 */
.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20rpx 0 40rpx;
}

.remember-password {
  display: flex;
  align-items: center;
}

.remember-text {
  font-size: 26rpx;
  color: #666;
  margin-left: 10rpx;
}

.forgot-password {
  font-size: 26rpx;
  color: #ff6b35;
  text-decoration: underline;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  background-color: #ff6b35;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 25rpx 0;
  font-size: 32rpx;
  font-weight: bold;
}

.login-btn[disabled] {
  background-color: #ccc;
}

/* 注册链接 */
.register-link {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30rpx;
}

.register-text {
  font-size: 26rpx;
  color: #999;
  margin-right: 10rpx;
}

.register-btn {
  font-size: 26rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 协议条款 */
.agreement {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 30rpx 0;
}

.agreement-text {
  font-size: 24rpx;
  color: #999;
  margin: 0 5rpx;
}

.agreement-link {
  font-size: 24rpx;
  color: #ff6b35;
  text-decoration: underline;
}

/* 快速体验 */
.quick-experience {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.quick-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

.quick-btn {
  font-size: 26rpx;
  color: #fff;
  text-decoration: underline;
}
