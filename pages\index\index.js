// index.js
Page({
  data: {
    searchValue: '',
    // 轮播图数据
    banners: [
      {
        id: 1,
        image: 'https://img2.baidu.com/it/u=101038931,1009737048&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=750'
      },
      {
        id: 2,
        image: 'https://qcloud.dpfile.com/pc/r5VrdFfL8X8p2y3MAZFe8TUxwAkDz-0O8JyoNtQ8CZY4RbSN-3XhH1U9rUaplI-0.jpg'
      },
      {
        id: 3,
        image: 'https://img0.baidu.com/it/u=3078606550,2655975750&fm=253&app=138&f=JPEG?w=800&h=1200'
      }
    ],
    // 九宫格导航数据
    navItems: [
      { id: 1, name: '沙拉', icon: '/images/salad.png', type: 'salad' },
      { id: 2, name: '汉堡', icon: '/images/burger.png', type: 'burger' },
      { id: 3, name: '饮品', icon: '/images/drink.png', type: 'drink' },
      { id: 4, name: '甜品', icon: '/images/dessert.png', type: 'dessert' },
      { id: 5, name: '套餐', icon: '/images/combo.png', type: 'combo' },
      { id: 6, name: '热销', icon: '/images/hot.png', type: 'hot' }
    ],
    // 推荐商品数据
    products: [
      {
        id: 1,
        name: '凯撒沙拉',
        description: '新鲜蔬菜配特制凯撒酱',
        price: 28.00,
        image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=400&fit=crop'
      },
      {
        id: 2,
        name: '牛油果吐司',
        description: '营养丰富的牛油果配全麦吐司',
        price: 32.00,
        image: 'https://images.unsplash.com/photo-1541519227354-08fa5d50c44d?w=400&h=400&fit=crop'
      },
      {
        id: 3,
        name: '鸡胸肉沙拉',
        description: '高蛋白低脂肪，健身首选',
        price: 35.00,
        image: 'https://images.unsplash.com/photo-1546793665-c74683f339c1?w=400&h=400&fit=crop'
      },
      {
        id: 4,
        name: '蔬菜卷',
        description: '多种新鲜蔬菜卷制而成',
        price: 25.00,
        image: 'https://images.unsplash.com/photo-1565299507177-b0ac66763828?w=400&h=400&fit=crop'
      }
    ]
  },

  onLoad() {
    console.log('首页加载完成')
  },

  // 轮播图点击事件
  onBannerTap(e) {
    const id = e.currentTarget.dataset.id
    console.log('点击轮播图:', id)
    // 可以跳转到对应的活动页面
  },

  // 搜索输入事件
  onSearchInput(e) {
    this.setData({
      searchValue: e.detail.value
    })
  },

  // 导航点击事件
  onNavTap(e) {
    const type = e.currentTarget.dataset.type
    console.log('点击导航:', type)
    wx.navigateTo({
      url: `/pages/category/category?type=${type}`
    })
  },

  // 更多商品点击事件
  onMoreTap() {
    wx.switchTab({
      url: '/pages/category/category'
    })
  },

  // 商品点击事件
  onProductTap(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/detail/detail?id=${id}`
    })
  },

  // 添加到购物车事件
  onAddToCart(e) {
    const id = e.currentTarget.dataset.id
    const product = this.data.products.find(item => item.id === id)

    if (product) {
      // 获取全局购物车数据
      const app = getApp()
      app.addToCart(product)

      wx.showToast({
        title: '已添加到购物车',
        icon: 'success',
        duration: 1500
      })
    }
  }
})
