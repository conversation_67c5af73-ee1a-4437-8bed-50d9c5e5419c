// pages/category/category.js
Page({
  data: {
    currentCategory: 'all',
    sortOrder: 'asc', // asc: 价格从低到高, desc: 价格从高到低
    sortText: '价格从低到高',
    categories: [
      { type: 'all', name: '全部' },
      { type: 'salad', name: '沙拉' },
      { type: 'burger', name: '汉堡' },
      { type: 'drink', name: '饮品' },
      { type: 'dessert', name: '甜品' },
      { type: 'combo', name: '套餐' }
    ],
    allProducts: [
      {
        id: 1,
        name: '凯撒沙拉',
        description: '新鲜蔬菜配特制凯撒酱',
        price: 28.00,
        category: 'salad',
        image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=400&fit=crop'
      },
      {
        id: 2,
        name: '牛油果吐司',
        description: '营养丰富的牛油果配全麦吐司',
        price: 32.00,
        category: 'salad',
        image: 'https://images.unsplash.com/photo-1541519227354-08fa5d50c44d?w=400&h=400&fit=crop'
      },
      {
        id: 3,
        name: '鸡胸肉沙拉',
        description: '高蛋白低脂肪，健身首选',
        price: 35.00,
        category: 'salad',
        image: 'https://images.unsplash.com/photo-1546793665-c74683f339c1?w=400&h=400&fit=crop'
      },
      {
        id: 4,
        name: '经典汉堡',
        description: '经典牛肉汉堡，口感丰富',
        price: 42.00,
        category: 'burger',
        image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=400&h=400&fit=crop'
      },
      {
        id: 5,
        name: '鸡肉汉堡',
        description: '嫩滑鸡胸肉配新鲜蔬菜',
        price: 38.00,
        category: 'burger',
        image: 'https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=400&h=400&fit=crop'
      },
      {
        id: 6,
        name: '鲜榨橙汁',
        description: '100%纯天然橙汁',
        price: 18.00,
        category: 'drink',
        image: 'https://images.unsplash.com/photo-1613478223719-2ab802602423?w=400&h=400&fit=crop'
      },
      {
        id: 7,
        name: '柠檬蜂蜜茶',
        description: '清香柠檬配天然蜂蜜',
        price: 22.00,
        category: 'drink',
        image: 'https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=400&h=400&fit=crop'
      },
      {
        id: 8,
        name: '提拉米苏',
        description: '经典意式甜品',
        price: 45.00,
        category: 'dessert',
        image: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?w=400&h=400&fit=crop'
      }
    ],
    filteredProducts: []
  },

  onLoad(options) {
    // 获取传入的分类参数
    const type = options.type || 'all'
    this.setData({
      currentCategory: type
    })
    this.filterProducts()
  },

  // 分类切换
  onCategoryTap(e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      currentCategory: type
    })
    this.filterProducts()
  },

  // 排序切换
  onSortTap() {
    const newOrder = this.data.sortOrder === 'asc' ? 'desc' : 'asc'
    const newText = newOrder === 'asc' ? '价格从低到高' : '价格从高到低'

    this.setData({
      sortOrder: newOrder,
      sortText: newText
    })
    this.filterProducts()
  },

  // 筛选功能（暂时只是提示）
  onFilterTap() {
    wx.showToast({
      title: '筛选功能开发中',
      icon: 'none'
    })
  },

  // 过滤和排序商品
  filterProducts() {
    let products = [...this.data.allProducts]

    // 按分类过滤
    if (this.data.currentCategory !== 'all') {
      products = products.filter(item => item.category === this.data.currentCategory)
    }

    // 按价格排序
    products.sort((a, b) => {
      return this.data.sortOrder === 'asc' ? a.price - b.price : b.price - a.price
    })

    this.setData({
      filteredProducts: products
    })
  },

  // 商品点击
  onProductTap(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/detail/detail?id=${id}`
    })
  },

  // 添加到购物车
  onAddToCart(e) {
    const id = e.currentTarget.dataset.id
    const product = this.data.allProducts.find(item => item.id === id)

    if (product) {
      const app = getApp()
      app.addToCart(product)

      wx.showToast({
        title: '已添加到购物车',
        icon: 'success',
        duration: 1500
      })
    }
  },

  onPullDownRefresh() {
    this.filterProducts()
    wx.stopPullDownRefresh()
  }
})