<!--profile.wxml-->
<scroll-view class="container" scroll-y="true">
  <!-- 用户信息头部 -->
  <view class="user-header">
    <view class="user-info">
      <image src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" class="user-avatar" mode="aspectFill"></image>
      <view class="user-details">
        <text class="user-name">{{userInfo.nickName || '点击登录'}}</text>
        <text class="user-phone">{{userInfo.phone || '未绑定手机号'}}</text>
      </view>
    </view>
    <view class="login-btn" bindtap="onLogin" wx:if="{{!isLoggedIn}}">
      <text class="login-text">登录</text>
    </view>
  </view>

  <!-- 订单统计 -->
  <view class="order-stats">
    <text class="stats-title">我的订单</text>
    <view class="stats-list">
      <view class="stats-item" bindtap="onOrderTap" data-status="all">
        <text class="stats-number">{{orderStats.total}}</text>
        <text class="stats-label">全部订单</text>
      </view>
      <view class="stats-item" bindtap="onOrderTap" data-status="pending">
        <text class="stats-number">{{orderStats.pending}}</text>
        <text class="stats-label">待付款</text>
      </view>
      <view class="stats-item" bindtap="onOrderTap" data-status="processing">
        <text class="stats-number">{{orderStats.processing}}</text>
        <text class="stats-label">制作中</text>
      </view>
      <view class="stats-item" bindtap="onOrderTap" data-status="completed">
        <text class="stats-number">{{orderStats.completed}}</text>
        <text class="stats-label">已完成</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-item" bindtap="onMenuTap" data-type="address">
      <view class="menu-left">
        <icon type="success" size="20" color="#ff6b35"></icon>
        <text class="menu-text">收货地址</text>
      </view>
      <icon type="next" size="16" color="#ccc"></icon>
    </view>

    <view class="menu-item" bindtap="onMenuTap" data-type="coupon">
      <view class="menu-left">
        <icon type="download" size="20" color="#ff6b35"></icon>
        <text class="menu-text">优惠券</text>
      </view>
      <view class="menu-right">
        <text class="menu-badge" wx:if="{{couponCount > 0}}">{{couponCount}}</text>
        <icon type="next" size="16" color="#ccc"></icon>
      </view>
    </view>

    <view class="menu-item" bindtap="onMenuTap" data-type="favorite">
      <view class="menu-left">
        <icon type="success_no_circle" size="20" color="#ff6b35"></icon>
        <text class="menu-text">我的收藏</text>
      </view>
      <icon type="next" size="16" color="#ccc"></icon>
    </view>

    <view class="menu-item" bindtap="onMenuTap" data-type="history">
      <view class="menu-left">
        <icon type="info" size="20" color="#ff6b35"></icon>
        <text class="menu-text">浏览历史</text>
      </view>
      <icon type="next" size="16" color="#ccc"></icon>
    </view>
  </view>

  <!-- 设置菜单 -->
  <view class="menu-section">
    <view class="menu-item" bindtap="onMenuTap" data-type="feedback">
      <view class="menu-left">
        <icon type="info_circle" size="20" color="#999"></icon>
        <text class="menu-text">意见反馈</text>
      </view>
      <icon type="next" size="16" color="#ccc"></icon>
    </view>

    <view class="menu-item" bindtap="onMenuTap" data-type="about">
      <view class="menu-left">
        <icon type="info" size="20" color="#999"></icon>
        <text class="menu-text">关于我们</text>
      </view>
      <icon type="next" size="16" color="#ccc"></icon>
    </view>

    <view class="menu-item" bindtap="onMenuTap" data-type="settings">
      <view class="menu-left">
        <icon type="setting" size="20" color="#999"></icon>
        <text class="menu-text">设置</text>
      </view>
      <icon type="next" size="16" color="#ccc"></icon>
    </view>
  </view>

  <!-- 退出登录按钮 -->
  <view class="logout-section" wx:if="{{isLoggedIn}}">
    <button class="logout-btn" bindtap="onLogout">退出登录</button>
  </view>
</scroll-view>