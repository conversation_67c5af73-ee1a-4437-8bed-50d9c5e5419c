/* cart.wxss */
page {
  height: 100vh;
  background-color: #f8f8f8;
}

.container {
  height: calc(100vh - 120rpx);
  display: flex;
  flex-direction: column;
}

/* 购物车列表样式 */
.cart-list {
  flex: 1;
  padding: 20rpx;
}

.cart-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.item-checkbox {
  margin-right: 20rpx;
}

.item-content {
  flex: 1;
  display: flex;
}

.item-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 15rpx;
  margin-right: 20rpx;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.item-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.item-spec {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.item-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-price {
  font-size: 28rpx;
  font-weight: bold;
  color: #ff6b35;
}

.quantity-control {
  display: flex;
  align-items: center;
}

.quantity-btn {
  width: 50rpx;
  height: 50rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn.disabled {
  opacity: 0.5;
}

.quantity-text {
  font-size: 24rpx;
  color: #333;
}

.quantity-value {
  font-size: 24rpx;
  color: #333;
  margin: 0 20rpx;
  min-width: 40rpx;
  text-align: center;
}

.item-delete {
  margin-left: 20rpx;
}

/* 空购物车样式 */
.empty-cart {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.go-shopping-btn {
  background-color: #ff6b35;
  color: #fff;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  border: none;
}

/* 底部结算栏样式 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  z-index: 100;
}

.select-all {
  display: flex;
  align-items: center;
  margin-right: 40rpx;
}

.select-all-text {
  font-size: 28rpx;
  color: #333;
  margin-left: 10rpx;
}

.total-info {
  flex: 1;
  text-align: right;
}

.total-label {
  font-size: 24rpx;
  color: #666;
}

.total-price {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b35;
}

.checkout-btn {
  background-color: #ff6b35;
  color: #fff;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border: none;
  margin-left: 20rpx;
}

.checkout-btn.disabled {
  background-color: #ccc;
}