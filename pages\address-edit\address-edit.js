// pages/address-edit/address-edit.js
Page({
  data: {
    isEdit: false,
    addressId: null,
    formData: {
      name: '',
      phone: '',
      province: '',
      city: '',
      district: '',
      detail: '',
      isDefault: false
    },
    mapData: {
      latitude: 39.908823,
      longitude: 116.397470,
      markers: []
    },
    canSubmit: false
  },

  onLoad(options) {
    if (options.id) {
      // 编辑模式
      this.setData({
        isEdit: true,
        addressId: options.id
      })
      this.loadAddressData(options.id)
    } else {
      // 新增模式
      this.getCurrentLocation()
    }
  },

  // 加载地址数据（编辑模式）
  loadAddressData(addressId) {
    const app = getApp()
    const address = app.getAddressById(addressId)
    
    if (address) {
      this.setData({
        formData: {
          name: address.name,
          phone: address.phone,
          province: address.province,
          city: address.city,
          district: address.district,
          detail: address.detail,
          isDefault: address.isDefault
        }
      })
      this.checkCanSubmit()
    }
  },

  // 获取当前位置
  getCurrentLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        this.setData({
          'mapData.latitude': res.latitude,
          'mapData.longitude': res.longitude
        })
      },
      fail: () => {
        wx.showToast({
          title: '获取位置失败',
          icon: 'none'
        })
      }
    })
  },

  // 输入框变化
  onInputChange(e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value
    
    this.setData({
      [`formData.${field}`]: value
    })
    this.checkCanSubmit()
  },

  // 开关变化
  onSwitchChange(e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value
    
    this.setData({
      [`formData.${field}`]: value
    })
  },

  // 选择地区
  onSelectRegion() {
    const that = this
    wx.chooseLocation({
      success: (res) => {
        // 解析地址信息
        const address = res.address || ''
        const addressParts = this.parseAddress(address)
        
        this.setData({
          'formData.province': addressParts.province,
          'formData.city': addressParts.city,
          'formData.district': addressParts.district,
          'formData.detail': res.name || '',
          'mapData.latitude': res.latitude,
          'mapData.longitude': res.longitude
        })
        this.checkCanSubmit()
      },
      fail: () => {
        wx.showToast({
          title: '选择位置失败',
          icon: 'none'
        })
      }
    })
  },

  // 解析地址信息
  parseAddress(address) {
    // 简单的地址解析，实际应用中可以使用更精确的地址解析服务
    const result = {
      province: '',
      city: '',
      district: ''
    }
    
    // 常见省份
    const provinces = ['北京市', '上海市', '天津市', '重庆市', '广东省', '江苏省', '浙江省', '山东省', '河南省', '四川省', '湖北省', '湖南省', '福建省', '安徽省', '江西省', '云南省', '贵州省', '山西省', '河北省', '陕西省', '辽宁省', '吉林省', '黑龙江省', '内蒙古', '新疆', '西藏', '宁夏', '青海省', '甘肃省', '广西', '海南省', '台湾省', '香港', '澳门']
    
    for (let province of provinces) {
      if (address.includes(province)) {
        result.province = province
        break
      }
    }
    
    // 简单提取市区信息
    const cityMatch = address.match(/(\w+市)/g)
    const districtMatch = address.match(/(\w+区|\w+县)/g)
    
    if (cityMatch && cityMatch.length > 0) {
      result.city = cityMatch[0]
    }
    
    if (districtMatch && districtMatch.length > 0) {
      result.district = districtMatch[0]
    }
    
    return result
  },

  // 地图区域变化
  onMapRegionChange(e) {
    if (e.type === 'end') {
      this.setData({
        'mapData.latitude': e.detail.centerLocation.latitude,
        'mapData.longitude': e.detail.centerLocation.longitude
      })
    }
  },

  // 获取当前位置按钮
  onGetLocation() {
    wx.showLoading({
      title: '定位中...'
    })
    
    this.getCurrentLocation()
    
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '定位成功',
        icon: 'success'
      })
    }, 1000)
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { name, phone, province, city, district, detail } = this.data.formData
    const phoneReg = /^1[3-9]\d{9}$/
    
    const canSubmit = name.trim() && 
                     phoneReg.test(phone) && 
                     province && 
                     city && 
                     district && 
                     detail.trim()
    
    this.setData({
      canSubmit: canSubmit
    })
  },

  // 提交表单
  onSubmit(e) {
    if (!this.data.canSubmit) {
      wx.showToast({
        title: '请完善地址信息',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: this.data.isEdit ? '保存中...' : '添加中...'
    })

    setTimeout(() => {
      wx.hideLoading()
      
      const app = getApp()
      const formData = this.data.formData
      
      if (this.data.isEdit) {
        // 更新地址
        const result = app.updateAddress(this.data.addressId, formData)
        if (result) {
          wx.showToast({
            title: '保存成功',
            icon: 'success'
          })
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        } else {
          wx.showToast({
            title: '保存失败',
            icon: 'error'
          })
        }
      } else {
        // 添加地址
        const result = app.addAddress(formData)
        if (result) {
          wx.showToast({
            title: '添加成功',
            icon: 'success'
          })
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        } else {
          wx.showToast({
            title: '添加失败',
            icon: 'error'
          })
        }
      }
    }, 1000)
  }
})
