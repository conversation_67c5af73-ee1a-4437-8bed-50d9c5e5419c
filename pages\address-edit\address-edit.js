// pages/address-edit/address-edit.js
Page({
  data: {
    isEdit: false,
    addressId: null,
    formData: {
      name: '',
      phone: '',
      province: '',
      city: '',
      district: '',
      detail: '',
      isDefault: false
    },
    mapData: {
      latitude: 39.908823,
      longitude: 116.397470,
      markers: []
    },
    canSubmit: false
  },

  onLoad(options) {
    if (options.id) {
      // 编辑模式
      this.setData({
        isEdit: true,
        addressId: options.id
      })
      wx.setNavigationBarTitle({
        title: '编辑地址'
      })
      this.loadAddressData(options.id)
    } else {
      // 新增模式
      wx.setNavigationBarTitle({
        title: '添加地址'
      })
      this.getCurrentLocation()
    }
  },

  // 加载地址数据（编辑模式）
  loadAddressData(addressId) {
    const app = getApp()
    const address = app.getAddressById(addressId)
    
    if (address) {
      this.setData({
        formData: {
          name: address.name,
          phone: address.phone,
          province: address.province,
          city: address.city,
          district: address.district,
          detail: address.detail,
          isDefault: address.isDefault
        },
        'mapData.latitude': address.latitude || 39.908823,
        'mapData.longitude': address.longitude || 116.397470
      })
      this.checkCanSubmit()
    } else {
      wx.showToast({
        title: '地址信息不存在',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 获取当前位置
  getCurrentLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        this.setData({
          'mapData.latitude': res.latitude,
          'mapData.longitude': res.longitude
        })
      },
      fail: () => {
        wx.showToast({
          title: '获取位置失败',
          icon: 'none'
        })
      }
    })
  },

  // 输入框变化
  onInputChange(e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value
    
    this.setData({
      [`formData.${field}`]: value
    })
    this.checkCanSubmit()
  },

  // 开关变化
  onSwitchChange(e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value
    
    this.setData({
      [`formData.${field}`]: value
    })
  },

  // 选择地区
  onSelectRegion() {
    // 使用picker组件选择地区，这里提供一个简化的实现
    const regions = [
      ['北京市', '上海市', '天津市', '重庆市', '广东省', '江苏省', '浙江省', '山东省', '河南省', '四川省'],
      ['北京市', '上海市', '天津市', '重庆市', '广州市', '深圳市', '南京市', '杭州市', '济南市', '成都市'],
      ['东城区', '西城区', '朝阳区', '丰台区', '石景山区', '海淀区', '门头沟区', '房山区', '通州区', '顺义区']
    ]

    wx.showActionSheet({
      itemList: ['手动输入地区', '选择位置', '使用当前位置'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 手动输入
          this.showRegionInput()
        } else if (res.tapIndex === 1) {
          // 选择位置
          this.chooseLocationFromMap()
        } else if (res.tapIndex === 2) {
          // 使用当前位置
          this.useCurrentLocation()
        }
      }
    })
  },

  // 手动输入地区
  showRegionInput() {
    wx.showModal({
      title: '输入地区信息',
      content: '请按照"省 市 区"的格式输入，例如：北京市 北京市 朝阳区',
      editable: true,
      placeholderText: '北京市 北京市 朝阳区',
      success: (res) => {
        if (res.confirm && res.content) {
          const parts = res.content.trim().split(/\s+/)
          if (parts.length >= 3) {
            this.setData({
              'formData.province': parts[0],
              'formData.city': parts[1],
              'formData.district': parts[2]
            })
            this.checkCanSubmit()
          } else {
            wx.showToast({
              title: '格式不正确',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 从地图选择位置
  chooseLocationFromMap() {
    wx.chooseLocation({
      success: (res) => {
        // 解析地址信息
        const address = res.address || ''
        const addressParts = this.parseAddress(address)

        this.setData({
          'formData.province': addressParts.province,
          'formData.city': addressParts.city,
          'formData.district': addressParts.district,
          'formData.detail': res.name || this.data.formData.detail,
          'mapData.latitude': res.latitude,
          'mapData.longitude': res.longitude
        })
        this.checkCanSubmit()
      },
      fail: (err) => {
        console.log('选择位置失败:', err)
        wx.showToast({
          title: '选择位置失败',
          icon: 'none'
        })
      }
    })
  },

  // 使用当前位置
  useCurrentLocation() {
    wx.showLoading({
      title: '获取位置中...'
    })

    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        this.setData({
          'mapData.latitude': res.latitude,
          'mapData.longitude': res.longitude
        })

        // 这里可以调用逆地理编码API获取地址信息
        // 暂时使用默认值
        this.setData({
          'formData.province': '北京市',
          'formData.city': '北京市',
          'formData.district': '朝阳区'
        })

        wx.hideLoading()
        wx.showToast({
          title: '位置获取成功',
          icon: 'success'
        })
        this.checkCanSubmit()
      },
      fail: () => {
        wx.hideLoading()
        wx.showToast({
          title: '获取位置失败',
          icon: 'none'
        })
      }
    })
  },

  // 解析地址信息
  parseAddress(address) {
    const result = {
      province: '',
      city: '',
      district: ''
    }

    if (!address) return result

    // 常见省份（包含自治区、直辖市）
    const provinces = [
      '北京市', '上海市', '天津市', '重庆市',
      '广东省', '江苏省', '浙江省', '山东省', '河南省', '四川省',
      '湖北省', '湖南省', '福建省', '安徽省', '江西省', '云南省',
      '贵州省', '山西省', '河北省', '陕西省', '辽宁省', '吉林省',
      '黑龙江省', '甘肃省', '青海省', '海南省', '台湾省',
      '内蒙古自治区', '新疆维吾尔自治区', '西藏自治区', '宁夏回族自治区', '广西壮族自治区',
      '香港特别行政区', '澳门特别行政区'
    ]

    // 查找省份
    for (let province of provinces) {
      if (address.includes(province) || address.includes(province.replace(/省|市|自治区|特别行政区/g, ''))) {
        result.province = province
        break
      }
    }

    // 提取市信息
    const cityMatches = address.match(/([\u4e00-\u9fa5]+市)/g)
    if (cityMatches && cityMatches.length > 0) {
      // 如果是直辖市，市名和省名相同
      if (['北京市', '上海市', '天津市', '重庆市'].includes(result.province)) {
        result.city = result.province
      } else {
        result.city = cityMatches[cityMatches.length - 1] // 取最后一个市
      }
    }

    // 提取区县信息
    const districtMatches = address.match(/([\u4e00-\u9fa5]+[区县])/g)
    if (districtMatches && districtMatches.length > 0) {
      result.district = districtMatches[districtMatches.length - 1] // 取最后一个区县
    }

    // 如果解析失败，提供默认值
    if (!result.province) result.province = '北京市'
    if (!result.city) result.city = '北京市'
    if (!result.district) result.district = '朝阳区'

    return result
  },

  // 地图区域变化
  onMapRegionChange(e) {
    if (e.type === 'end') {
      this.setData({
        'mapData.latitude': e.detail.centerLocation.latitude,
        'mapData.longitude': e.detail.centerLocation.longitude
      })
    }
  },

  // 获取当前位置按钮
  onGetLocation() {
    wx.showLoading({
      title: '定位中...'
    })
    
    this.getCurrentLocation()
    
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '定位成功',
        icon: 'success'
      })
    }, 1000)
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { name, phone, province, city, district, detail } = this.data.formData
    const phoneReg = /^1[3-9]\d{9}$/
    
    const canSubmit = name.trim() && 
                     phoneReg.test(phone) && 
                     province && 
                     city && 
                     district && 
                     detail.trim()
    
    this.setData({
      canSubmit: canSubmit
    })
  },

  // 提交表单
  onSubmit(e) {
    // 详细验证
    const { name, phone, province, city, district, detail } = this.data.formData

    if (!name.trim()) {
      wx.showToast({
        title: '请输入收货人姓名',
        icon: 'none'
      })
      return
    }

    const phoneReg = /^1[3-9]\d{9}$/
    if (!phoneReg.test(phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return
    }

    if (!province || !city || !district) {
      wx.showToast({
        title: '请选择所在地区',
        icon: 'none'
      })
      return
    }

    if (!detail.trim()) {
      wx.showToast({
        title: '请输入详细地址',
        icon: 'none'
      })
      return
    }

    if (!this.data.canSubmit) {
      wx.showToast({
        title: '请完善地址信息',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: this.data.isEdit ? '保存中...' : '添加中...'
    })

    setTimeout(() => {
      wx.hideLoading()
      
      const app = getApp()
      const formData = this.data.formData
      
      if (this.data.isEdit) {
        // 更新地址
        const result = app.updateAddress(this.data.addressId, formData)
        if (result) {
          wx.showToast({
            title: '保存成功',
            icon: 'success'
          })
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        } else {
          wx.showModal({
            title: '保存失败',
            content: '地址保存失败，请检查网络连接后重试',
            confirmText: '重试',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                this.onSubmit()
              }
            }
          })
        }
      } else {
        // 添加地址
        const result = app.addAddress(formData)
        if (result) {
          wx.showToast({
            title: '添加成功',
            icon: 'success'
          })
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        } else {
          wx.showModal({
            title: '添加失败',
            content: '地址添加失败，请检查网络连接后重试',
            confirmText: '重试',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                this.onSubmit()
              }
            }
          })
        }
      }
    }, 1000)
  }
})
