<!--index.wxml-->
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 轮播图 -->
    <swiper class="banner-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="3000" duration="500">
      <swiper-item wx:for="{{banners}}" wx:key="id">
        <image src="{{item.image}}" class="banner-image" mode="aspectFill" bindtap="onBannerTap" data-id="{{item.id}}"></image>
      </swiper-item>
    </swiper>

    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <icon type="search" size="16" color="#999"></icon>
        <input placeholder="搜索美食" placeholder-class="search-placeholder" bindinput="onSearchInput" value="{{searchValue}}"></input>
      </view>
    </view>

    <!-- 九宫格导航 -->
    <view class="nav-grid">
      <view class="nav-item" wx:for="{{navItems}}" wx:key="id" bindtap="onNavTap" data-type="{{item.type}}">
        <image src="{{item.icon}}" class="nav-icon"></image>
        <text class="nav-text">{{item.name}}</text>
      </view>
    </view>

    <!-- 推荐商品标题 -->
    <view class="section-title">
      <text class="title-text">推荐美食</text>
      <text class="title-more" bindtap="onMoreTap">更多 ></text>
    </view>

    <!-- 商品列表 -->
    <view class="product-list">
      <view class="product-item" wx:for="{{products}}" wx:key="id" bindtap="onProductTap" data-id="{{item.id}}">
        <image src="{{item.image}}" class="product-image" mode="aspectFill"></image>
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <text class="product-desc">{{item.description}}</text>
          <view class="product-bottom">
            <text class="product-price">¥{{item.price}}</text>
            <view class="add-cart-btn" bindtap="onAddToCart" data-id="{{item.id}}" catchtap="true">
              <text class="add-cart-text">+</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</scroll-view>
