// pages/detail/detail.js
Page({
  data: {
    product: {},
    quantity: 1,
    selectedSpec: null,
    totalPrice: 0,
    // 模拟商品数据
    products: [
      {
        id: 1,
        name: '凯撒沙拉',
        description: '新鲜蔬菜配特制凯撒酱，营养丰富，口感清爽',
        price: 28.00,
        image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=400&fit=crop',
        detail: '精选新鲜生菜、番茄、黄瓜等蔬菜，搭配特制凯撒酱汁，营养均衡，是健康饮食的首选。富含维生素和纤维，有助于消化和美容。',
        nutrition: [
          { name: '热量', value: '120 kcal' },
          { name: '蛋白质', value: '8g' },
          { name: '脂肪', value: '6g' },
          { name: '碳水化合物', value: '12g' }
        ],
        specs: [
          { id: 1, name: '标准', extraPrice: 0 },
          { id: 2, name: '加量', extraPrice: 5 }
        ]
      },
      {
        id: 2,
        name: '牛油果吐司',
        description: '营养丰富的牛油果配全麦吐司',
        price: 32.00,
        image: 'https://images.unsplash.com/photo-1541519227354-08fa5d50c44d?w=400&h=400&fit=crop',
        detail: '选用新鲜牛油果和优质全麦面包，富含健康脂肪和纤维，是早餐或轻食的完美选择。',
        nutrition: [
          { name: '热量', value: '280 kcal' },
          { name: '蛋白质', value: '12g' },
          { name: '脂肪', value: '18g' },
          { name: '碳水化合物', value: '25g' }
        ]
      },
      {
        id: 3,
        name: '鸡胸肉沙拉',
        description: '高蛋白低脂肪，健身首选',
        price: 35.00,
        image: 'https://images.unsplash.com/photo-1546793665-c74683f339c1?w=400&h=400&fit=crop',
        detail: '精选优质鸡胸肉，低温慢煮保持嫩滑口感，搭配多种新鲜蔬菜，是健身人士的理想选择。',
        nutrition: [
          { name: '热量', value: '180 kcal' },
          { name: '蛋白质', value: '25g' },
          { name: '脂肪', value: '4g' },
          { name: '碳水化合物', value: '8g' }
        ],
        specs: [
          { id: 1, name: '100g', extraPrice: 0 },
          { id: 2, name: '150g', extraPrice: 8 },
          { id: 3, name: '200g', extraPrice: 15 }
        ]
      }
    ]
  },

  onLoad(options) {
    const id = parseInt(options.id)
    const product = this.data.products.find(item => item.id === id)

    if (product) {
      this.setData({
        product: product,
        selectedSpec: product.specs ? product.specs[0].id : null
      })
      this.calculateTotalPrice()
    } else {
      wx.showToast({
        title: '商品不存在',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 返回上一页
  onBack() {
    wx.navigateBack()
  },

  // 规格选择
  onSpecTap(e) {
    const specId = e.currentTarget.dataset.id
    this.setData({
      selectedSpec: specId
    })
    this.calculateTotalPrice()
  },

  // 数量调整
  onQuantityChange(e) {
    const type = e.currentTarget.dataset.type
    let quantity = this.data.quantity

    if (type === 'plus') {
      quantity++
    } else if (type === 'minus' && quantity > 1) {
      quantity--
    }

    this.setData({
      quantity: quantity
    })
    this.calculateTotalPrice()
  },

  // 计算总价
  calculateTotalPrice() {
    let price = this.data.product.price

    // 加上规格额外费用
    if (this.data.selectedSpec && this.data.product.specs) {
      const spec = this.data.product.specs.find(item => item.id === this.data.selectedSpec)
      if (spec) {
        price += spec.extraPrice
      }
    }

    const totalPrice = (price * this.data.quantity).toFixed(2)
    this.setData({
      totalPrice: totalPrice
    })
  },

  // 添加到购物车
  onAddToCart() {
    const cartItem = this.buildCartItem()
    const app = getApp()
    app.addToCart(cartItem)

    wx.showToast({
      title: '已添加到购物车',
      icon: 'success',
      duration: 1500
    })
  },

  // 立即购买
  onBuyNow() {
    const cartItem = this.buildCartItem()

    // 这里可以跳转到订单确认页面
    wx.showToast({
      title: '立即购买功能开发中',
      icon: 'none'
    })
  },

  // 构建购物车商品对象
  buildCartItem() {
    const product = this.data.product
    let spec = null

    if (this.data.selectedSpec && product.specs) {
      spec = product.specs.find(item => item.id === this.data.selectedSpec)
    }

    return {
      id: product.id,
      name: product.name,
      image: product.image,
      price: product.price,
      quantity: this.data.quantity,
      spec: spec,
      totalPrice: this.data.totalPrice
    }
  }
})