// pages/login/login.js
Page({
  data: {
    loginType: 'wechat', // 'phone' | 'wechat'
    phone: '',
    code: '',
    agreed: true,
    canSendCode: false,
    canLogin: false,
    codeText: '获取验证码',
    countdown: 0
  },

  onLoad(options) {
    // 检查是否从其他页面跳转过来
    this.fromPage = options.from || 'profile'
  },

  // 手机号输入
  onPhoneInput(e) {
    const phone = e.detail.value
    this.setData({
      phone: phone,
      canSendCode: this.validatePhone(phone)
    })
    this.checkCanLogin()
  },

  // 验证码输入
  onCodeInput(e) {
    const code = e.detail.value
    this.setData({
      code: code
    })
    this.checkCanLogin()
  },

  // 验证手机号
  validatePhone(phone) {
    return /^1[3-9]\d{9}$/.test(phone)
  },

  // 检查是否可以登录
  checkCanLogin() {
    const canLogin = this.data.loginType === 'phone' 
      ? this.validatePhone(this.data.phone) && this.data.code.length === 6 && this.data.agreed
      : this.data.agreed
    
    this.setData({
      canLogin: canLogin
    })
  },

  // 发送验证码
  onSendCode() {
    if (!this.data.canSendCode || this.data.countdown > 0) return

    // 模拟发送验证码
    wx.showLoading({
      title: '发送中...'
    })

    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '验证码已发送',
        icon: 'success'
      })

      // 开始倒计时
      this.startCountdown()
    }, 1000)
  },

  // 开始倒计时
  startCountdown() {
    let countdown = 60
    this.setData({
      countdown: countdown,
      codeText: `${countdown}s后重发`
    })

    const timer = setInterval(() => {
      countdown--
      if (countdown <= 0) {
        clearInterval(timer)
        this.setData({
          countdown: 0,
          codeText: '获取验证码'
        })
      } else {
        this.setData({
          countdown: countdown,
          codeText: `${countdown}s后重发`
        })
      }
    }, 1000)
  },

  // 手机号登录
  onPhoneLogin() {
    if (!this.data.canLogin) return

    wx.showLoading({
      title: '登录中...'
    })

    // 模拟登录请求
    setTimeout(() => {
      wx.hideLoading()
      
      const userInfo = {
        avatarUrl: '/images/default-avatar.png',
        nickName: `用户${this.data.phone.substr(-4)}`,
        phone: this.data.phone,
        loginType: 'phone'
      }

      this.loginSuccess(userInfo)
    }, 1500)
  },

  // 微信登录
  onWechatLogin(e) {
    if (!this.data.agreed) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    if (e.detail.userInfo) {
      const userInfo = {
        avatarUrl: e.detail.userInfo.avatarUrl,
        nickName: e.detail.userInfo.nickName,
        phone: '', // 微信登录暂不获取手机号
        loginType: 'wechat'
      }

      this.loginSuccess(userInfo)
    } else {
      wx.showToast({
        title: '登录取消',
        icon: 'none'
      })
    }
  },

  // 登录成功
  loginSuccess(userInfo) {
    // 保存用户信息
    wx.setStorageSync('userInfo', userInfo)
    
    wx.showToast({
      title: '登录成功',
      icon: 'success',
      duration: 1500
    })

    // 返回上一页或跳转到个人中心
    setTimeout(() => {
      if (this.fromPage === 'profile') {
        wx.switchTab({
          url: '/pages/profile/profile'
        })
      } else {
        wx.navigateBack()
      }
    }, 1500)
  },

  // 切换登录方式
  onSwitchLogin(e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      loginType: type
    })
    this.checkCanLogin()
  },

  // 协议变更
  onAgreementChange(e) {
    const agreed = e.detail.value.includes('agree')
    this.setData({
      agreed: agreed
    })
    this.checkCanLogin()
  },

  // 显示协议
  onShowAgreement(e) {
    const type = e.currentTarget.dataset.type
    const title = type === 'user' ? '用户协议' : '隐私政策'
    const content = type === 'user' 
      ? '这里是用户协议的内容...\n\n1. 用户权利和义务\n2. 服务条款\n3. 免责声明'
      : '这里是隐私政策的内容...\n\n1. 信息收集\n2. 信息使用\n3. 信息保护'

    wx.showModal({
      title: title,
      content: content,
      showCancel: false
    })
  },

  // 快速体验
  onQuickExperience() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
})
