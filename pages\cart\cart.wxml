<!--cart.wxml-->
<view class="container">
  <!-- 收货地址 -->
  <view class="address-section" wx:if="{{cartItems.length > 0}}">
    <view class="address-content" bindtap="onSelectAddress">
      <view class="address-icon">
        <icon type="success_no_circle" size="20" color="#ff6b35"></icon>
      </view>
      <view class="address-info" wx:if="{{selectedAddress}}">
        <view class="address-header">
          <text class="contact-name">{{selectedAddress.name}}</text>
          <text class="contact-phone">{{selectedAddress.phone}}</text>
        </view>
        <text class="address-detail">{{selectedAddress.fullAddress}}</text>
      </view>
      <view class="address-info" wx:else>
        <text class="address-placeholder">请选择收货地址</text>
      </view>
      <view class="address-arrow">
        <icon type="next" size="16" color="#ccc"></icon>
      </view>
    </view>
  </view>

  <!-- 购物车商品列表 -->
  <scroll-view class="cart-list" scroll-y="true" wx:if="{{cartItems.length > 0}}">
    <view class="cart-item" wx:for="{{cartItems}}" wx:key="cartId">
      <!-- 选择框 -->
      <view class="item-checkbox" bindtap="onSelectItem" data-id="{{item.cartId}}">
        <icon type="{{item.selected ? 'success' : 'circle'}}" size="20" color="{{item.selected ? '#ff6b35' : '#ccc'}}"></icon>
      </view>

      <!-- 商品信息 -->
      <view class="item-content">
        <image src="{{item.image}}" class="item-image" mode="aspectFill"></image>
        <view class="item-info">
          <text class="item-name">{{item.name}}</text>
          <text class="item-spec" wx:if="{{item.spec}}">{{item.spec.name}}</text>
          <view class="item-bottom">
            <text class="item-price">¥{{item.price}}</text>
            <view class="quantity-control">
              <view class="quantity-btn {{item.quantity <= 1 ? 'disabled' : ''}}"
                    bindtap="onQuantityChange" data-id="{{item.cartId}}" data-type="minus">
                <text class="quantity-text">-</text>
              </view>
              <text class="quantity-value">{{item.quantity}}</text>
              <view class="quantity-btn" bindtap="onQuantityChange" data-id="{{item.cartId}}" data-type="plus">
                <text class="quantity-text">+</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 删除按钮 -->
      <view class="item-delete" bindtap="onDeleteItem" data-id="{{item.cartId}}">
        <icon type="cancel" size="20" color="#999"></icon>
      </view>
    </view>
  </scroll-view>

  <!-- 空购物车状态 -->
  <view class="empty-cart" wx:else>
    <image src="/images/empty-cart.png" class="empty-image"></image>
    <text class="empty-text">购物车空空如也</text>
    <button class="go-shopping-btn" bindtap="onGoShopping">去逛逛</button>
  </view>
</view>

<!-- 底部结算栏 -->
<view class="bottom-bar" wx:if="{{cartItems.length > 0}}">
  <view class="select-all" bindtap="onSelectAll">
    <icon type="{{allSelected ? 'success' : 'circle'}}" size="20" color="{{allSelected ? '#ff6b35' : '#ccc'}}"></icon>
    <text class="select-all-text">全选</text>
  </view>

  <view class="total-info">
    <text class="total-label">合计：</text>
    <text class="total-price">¥{{totalPrice}}</text>
  </view>

  <button class="checkout-btn {{selectedCount === 0 ? 'disabled' : ''}}"
          bindtap="onCheckout" disabled="{{selectedCount === 0}}">
    结算({{selectedCount}})
  </button>
</view>