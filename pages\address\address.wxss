/* address.wxss */
page {
  height: 100vh;
  background-color: #f8f8f8;
}

.container {
  height: calc(100vh - 120rpx);
  display: flex;
  flex-direction: column;
}

/* 地址列表样式 */
.address-list {
  flex: 1;
  padding: 20rpx;
}

.address-item {
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.address-content {
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.contact-info {
  display: flex;
  align-items: center;
}

.contact-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.contact-phone {
  font-size: 28rpx;
  color: #666;
}

.default-tag {
  background-color: #ff6b35;
  color: #fff;
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.address-detail {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 操作按钮样式 */
.address-actions {
  display: flex;
  background-color: #f8f8f8;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 25rpx 0;
  background-color: transparent;
  border: none;
  font-size: 26rpx;
  color: #666;
  border-right: 1rpx solid #eee;
}

.action-btn:last-child {
  border-right: none;
}

.action-btn icon {
  margin-right: 8rpx;
}

.edit-btn {
  color: #ff6b35;
}

.default-btn.active {
  color: #ff6b35;
}

.delete-btn {
  color: #999;
}

.action-btn[disabled] {
  opacity: 0.6;
}

/* 空状态样式 */
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 15rpx;
}

.empty-tip {
  font-size: 26rpx;
  color: #ccc;
}

/* 底部按钮样式 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  z-index: 100;
}

.add-address-btn {
  width: 100%;
  background-color: #ff6b35;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 25rpx 0;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-address-btn icon {
  margin-right: 15rpx;
}
