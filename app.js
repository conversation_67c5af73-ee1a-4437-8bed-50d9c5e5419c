// app.js
App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
      }
    })

    // 初始化购物车
    this.initCart()
  },

  globalData: {
    userInfo: null,
    cartItems: [],
    orders: []
  },

  // 初始化购物车
  initCart() {
    const cartItems = wx.getStorageSync('cartItems') || []
    const orders = wx.getStorageSync('orders') || []
    this.globalData.cartItems = cartItems
    this.globalData.orders = orders
  },

  // 添加商品到购物车
  addToCart(product) {
    const cartItems = this.globalData.cartItems
    const cartId = Date.now() + Math.random() // 生成唯一ID

    // 检查是否已存在相同商品和规格
    const existingIndex = cartItems.findIndex(item =>
      item.id === product.id &&
      JSON.stringify(item.spec) === JSON.stringify(product.spec)
    )

    if (existingIndex > -1) {
      // 如果存在，增加数量
      cartItems[existingIndex].quantity += product.quantity || 1
      cartItems[existingIndex].totalPrice = (
        (cartItems[existingIndex].price + (cartItems[existingIndex].spec?.extraPrice || 0)) *
        cartItems[existingIndex].quantity
      ).toFixed(2)
    } else {
      // 如果不存在，添加新商品
      const cartItem = {
        cartId: cartId,
        id: product.id,
        name: product.name,
        image: product.image,
        price: product.price,
        quantity: product.quantity || 1,
        spec: product.spec || null,
        selected: true,
        totalPrice: product.totalPrice || (product.price * (product.quantity || 1)).toFixed(2)
      }
      cartItems.push(cartItem)
    }

    this.globalData.cartItems = cartItems
    this.saveCart()
  },

  // 获取购物车商品
  getCartItems() {
    return this.globalData.cartItems
  },

  // 更新购物车
  updateCart(cartItems) {
    this.globalData.cartItems = cartItems
    this.saveCart()
  },

  // 保存购物车到本地存储
  saveCart() {
    wx.setStorageSync('cartItems', this.globalData.cartItems)
    // 更新购物车角标
    this.updateCartBadge()
  },

  // 更新购物车角标
  updateCartBadge() {
    const count = this.getCartCount()
    if (count > 0) {
      wx.setTabBarBadge({
        index: 2, // 购物车tab的索引
        text: count.toString()
      })
    } else {
      wx.removeTabBarBadge({
        index: 2
      })
    }
  },

  // 获取购物车商品数量
  getCartCount() {
    return this.globalData.cartItems.reduce((count, item) => count + item.quantity, 0)
  },

  // 清空购物车
  clearCart() {
    this.globalData.cartItems = []
    this.saveCart()
  },

  // 创建订单
  createOrder(orderData) {
    const orderId = 'ORD' + Date.now()
    const order = {
      id: orderId,
      items: orderData.items,
      totalPrice: orderData.totalPrice,
      totalQuantity: orderData.totalQuantity,
      status: 'pending', // pending, processing, completed, cancelled
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
      userInfo: wx.getStorageSync('userInfo') || {},
      address: orderData.address || '默认地址',
      remark: orderData.remark || ''
    }

    this.globalData.orders.unshift(order)
    this.saveOrders()
    return order
  },

  // 更新订单状态
  updateOrderStatus(orderId, status) {
    const orderIndex = this.globalData.orders.findIndex(order => order.id === orderId)
    if (orderIndex > -1) {
      this.globalData.orders[orderIndex].status = status
      this.globalData.orders[orderIndex].updateTime = new Date().toISOString()
      this.saveOrders()
      return true
    }
    return false
  },

  // 获取订单列表
  getOrders(status = null) {
    if (status) {
      return this.globalData.orders.filter(order => order.status === status)
    }
    return this.globalData.orders
  },

  // 获取订单统计
  getOrderStats() {
    const orders = this.globalData.orders
    return {
      total: orders.length,
      pending: orders.filter(order => order.status === 'pending').length,
      processing: orders.filter(order => order.status === 'processing').length,
      completed: orders.filter(order => order.status === 'completed').length,
      cancelled: orders.filter(order => order.status === 'cancelled').length
    }
  },

  // 保存订单到本地存储
  saveOrders() {
    wx.setStorageSync('orders', this.globalData.orders)
  },

  // 删除订单
  deleteOrder(orderId) {
    const orderIndex = this.globalData.orders.findIndex(order => order.id === orderId)
    if (orderIndex > -1) {
      this.globalData.orders.splice(orderIndex, 1)
      this.saveOrders()
      return true
    }
    return false
  }
})
