// app.js
App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
      }
    })

    // 初始化购物车
    this.initCart()
  },

  globalData: {
    userInfo: null,
    cartItems: []
  },

  // 初始化购物车
  initCart() {
    const cartItems = wx.getStorageSync('cartItems') || []
    this.globalData.cartItems = cartItems
  },

  // 添加商品到购物车
  addToCart(product) {
    const cartItems = this.globalData.cartItems
    const cartId = Date.now() + Math.random() // 生成唯一ID

    // 检查是否已存在相同商品和规格
    const existingIndex = cartItems.findIndex(item =>
      item.id === product.id &&
      JSON.stringify(item.spec) === JSON.stringify(product.spec)
    )

    if (existingIndex > -1) {
      // 如果存在，增加数量
      cartItems[existingIndex].quantity += product.quantity || 1
      cartItems[existingIndex].totalPrice = (
        (cartItems[existingIndex].price + (cartItems[existingIndex].spec?.extraPrice || 0)) *
        cartItems[existingIndex].quantity
      ).toFixed(2)
    } else {
      // 如果不存在，添加新商品
      const cartItem = {
        cartId: cartId,
        id: product.id,
        name: product.name,
        image: product.image,
        price: product.price,
        quantity: product.quantity || 1,
        spec: product.spec || null,
        selected: true,
        totalPrice: product.totalPrice || (product.price * (product.quantity || 1)).toFixed(2)
      }
      cartItems.push(cartItem)
    }

    this.globalData.cartItems = cartItems
    this.saveCart()
  },

  // 获取购物车商品
  getCartItems() {
    return this.globalData.cartItems
  },

  // 更新购物车
  updateCart(cartItems) {
    this.globalData.cartItems = cartItems
    this.saveCart()
  },

  // 保存购物车到本地存储
  saveCart() {
    wx.setStorageSync('cartItems', this.globalData.cartItems)
  },

  // 获取购物车商品数量
  getCartCount() {
    return this.globalData.cartItems.reduce((count, item) => count + item.quantity, 0)
  },

  // 清空购物车
  clearCart() {
    this.globalData.cartItems = []
    this.saveCart()
  }
})
